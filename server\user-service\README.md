# 用户服务 (User Service)

DL（Digital Learning）引擎用户服务，负责用户管理、认证和授权功能。

## 功能特性

- 用户注册和登录
- JWT 认证
- 用户信息管理
- 用户头像管理
- 用户设置管理
- 角色权限控制
- 微服务架构支持
- 健康检查

## 技术栈

- Node.js 22
- NestJS 10
- TypeScript 5
- TypeORM 0.3
- MySQL 8.0
- JWT 认证
- Swagger API 文档

## 快速开始

### 环境要求

- Node.js 22+
- MySQL 8.0+
- Redis (可选，用于缓存)

### 安装依赖

```bash
npm install
```

### 环境配置

复制 `.env.example` 为 `.env` 并配置相关参数：

```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=your_password
DB_DATABASE=ir_engine_users

# JWT 配置
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=1d

# 服务配置
USER_SERVICE_HOST=localhost
USER_SERVICE_PORT=3001
USER_SERVICE_HTTP_PORT=4001
```

### 运行服务

```bash
# 开发模式
npm run start:dev

# 生产模式
npm run build
npm run start:prod
```

## API 文档

启动服务后访问 Swagger 文档：
- 开发环境: http://localhost:4001/api/docs
- 生产环境: http://your-domain/api/docs

## 测试

```bash
# 单元测试
npm test

# 测试覆盖率
npm run test:cov

# E2E 测试
npm run test:e2e
```

## Docker 部署

```bash
# 构建镜像
docker build -t user-service .

# 运行容器
docker run -p 3001:3001 -p 4001:4001 user-service
```

## 健康检查

- HTTP: GET /health
- 微服务: 自动注册到服务注册中心

## 最近修复的问题

1. ✅ 添加了缺失的 ESLint 配置文件 (`.eslintrc.js`)
2. ✅ 添加了缺失的 Prettier 配置文件 (`.prettierrc`)
3. ✅ 修复了 ESLint 错误（未使用的变量和导入）
4. ✅ 修复了单元测试中的模拟对象问题
5. ✅ 更新了用户响应 DTO 以匹配实际的用户实体
6. ✅ 添加了 Jest E2E 测试配置
7. ✅ 添加了健康检查脚本

## 项目结构

```
src/
├── auth/           # 认证模块
├── users/          # 用户模块
├── health/         # 健康检查模块
├── app.module.ts   # 应用主模块
├── app.service.ts  # 应用服务
└── main.ts         # 应用入口
```

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License
